/**
 * ScheduleManager - Manages NPC schedules and routines
 * Creates a living world where characters have their own lives and patterns
 */

export class ScheduleManager {
    constructor(stateManager) {
        this.stateManager = stateManager;
        
        // Base schedules for different character types
        const baseWeekdaySchedule = {
            family_member: {
                'Morning': 'Home',
                'School-P1': 'Home',
                'School-P2': 'Home',
                'School-P3': 'Home',
                'School-P4': 'Home',
                'Lunch': 'Home',
                'School-P5': 'Home',
                'Afternoon': 'Home',
                'Evening': 'Home',
                'Night': 'Home',
                'Late Night': 'Home'
            },
            working_sister: {
                'Morning': 'Home',
                'School-P1': 'Work',
                'School-P2': 'Work',
                'School-P3': 'Work',
                'School-P4': 'Work',
                'Lunch': 'Work',
                'School-P5': 'Work',
                'Afternoon': 'Work',
                'Evening': 'Home',
                'Night': 'Home',
                'Late Night': 'Home'
            },
            student: {
                'Morning': 'Home',
                'School-P1': 'School',
                'School-P2': 'School',
                'School-P3': 'School',
                'School-P4': 'School',
                'Lunch': 'School',
                'School-P5': 'School',
                'Afternoon': 'Home',
                'Evening': 'Home',
                'Night': 'Home',
                'Late Night': 'Home'
            }
        };

        const baseWeekendSchedule = {
            family_member: {
                'Morning': 'Home',
                'School-P1': 'Home',
                'School-P2': 'Home',
                'School-P3': 'Home',
                'School-P4': 'Home',
                'Lunch': 'Home',
                'School-P5': 'Home',
                'Afternoon': 'Home',
                'Evening': 'Home',
                'Night': 'Home',
                'Late Night': 'Home'
            },
            working_sister: {
                'Morning': 'Home',
                'School-P1': 'Home',
                'School-P2': 'Home',
                'School-P3': 'Home',
                'School-P4': 'Home',
                'Lunch': 'Home',
                'School-P5': 'Home',
                'Afternoon': 'Home',
                'Evening': 'Home',
                'Night': 'Home',
                'Late Night': 'Home'
            },
            student: {
                'Morning': 'Home',
                'School-P1': 'Home',
                'School-P2': 'Home',
                'School-P3': 'Home',
                'School-P4': 'Home',
                'Lunch': 'Home',
                'School-P5': 'Home',
                'Afternoon': 'Home',
                'Evening': 'Home',
                'Night': 'Home',
                'Late Night': 'Home'
            }
        };

        // Generate full weekly schedules
        this.baseSchedules = {};
        const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
        const weekends = ['Saturday', 'Sunday'];

        Object.keys(baseWeekdaySchedule).forEach(characterType => {
            this.baseSchedules[characterType] = {};

            // Weekdays
            weekdays.forEach(day => {
                this.baseSchedules[characterType][day] = { ...baseWeekdaySchedule[characterType] };
            });

            // Weekends
            weekends.forEach(day => {
                this.baseSchedules[characterType][day] = { ...baseWeekendSchedule[characterType] };
            });
        });
        
        // Character-specific schedule overrides
        this.characterSchedules = {
            sonia: {
                type: 'family_member',
                special_events: {
                    'Tuesday': { 'Evening': 'Temple' },
                    'Thursday': { 'Afternoon': 'Market' },
                    'Saturday': { 'Morning': 'Temple', 'Afternoon': 'Market' }
                }
            },
            
            natasha: {
                type: 'working_sister',
                special_events: {
                    'Friday': { 'Night': 'Mall' },
                    'Saturday': { 'Afternoon': 'Mall', 'Evening': 'Friends' },
                    'Sunday': { 'Morning': 'Home', 'Afternoon': 'Park' }
                }
            },
            
            tanya: {
                type: 'working_sister',
                special_events: {
                    'Wednesday': { 'Evening': 'Dance Class' },
                    'Saturday': { 'Morning': 'Gym', 'Evening': 'Club' },
                    'Sunday': { 'Afternoon': 'Home' }
                }
            },
            
            kavya: {
                type: 'student',
                special_events: {
                    'Wednesday': { 'Afternoon': 'Tuition' },
                    'Saturday': { 'Morning': 'Home', 'Afternoon': 'Mall' },
                    'Sunday': { 'Morning': 'Temple', 'Afternoon': 'Home' }
                }
            }
        };
        
        this.currentSchedules = {};
    }
    
    /**
     * Generate daily schedules for all characters
     */
    generateDailySchedules(dayInfo) {
        const { dayOfWeek } = dayInfo;
        
        Object.keys(this.characterSchedules).forEach(characterId => {
            this.generateCharacterSchedule(characterId, dayOfWeek);
        });
        
        console.log(`Generated schedules for ${dayOfWeek}`);
    }
    
    /**
     * Generate schedule for specific character
     */
    generateCharacterSchedule(characterId, dayOfWeek) {
        const characterConfig = this.characterSchedules[characterId];
        if (!characterConfig) return;
        
        // Start with base schedule for character type
        const baseSchedule = this.baseSchedules[characterConfig.type];
        if (!baseSchedule || !baseSchedule[dayOfWeek]) return;
        
        // Clone the base schedule
        let schedule = { ...baseSchedule[dayOfWeek] };
        
        // Apply special events for this day
        if (characterConfig.special_events && characterConfig.special_events[dayOfWeek]) {
            Object.assign(schedule, characterConfig.special_events[dayOfWeek]);
        }
        
        // Apply random variations (10% chance for minor changes)
        if (Math.random() < 0.1) {
            schedule = this.applyRandomVariation(schedule, characterId);
        }
        
        // Store the schedule
        if (!this.currentSchedules[characterId]) {
            this.currentSchedules[characterId] = {};
        }
        this.currentSchedules[characterId][dayOfWeek] = schedule;
        
        // Update character's schedule in state
        const character = this.stateManager.getCharacter(characterId);
        if (character) {
            // Initialize schedule property if it doesn't exist
            if (!character.schedule) {
                character.schedule = {};
            }
            character.schedule[dayOfWeek] = schedule;
        }
    }
    
    /**
     * Apply random variations to schedule
     */
    applyRandomVariation(schedule, characterId) {
        const variations = {
            sonia: {
                'Afternoon': ['Home', 'Market', 'Temple'],
                'Evening': ['Home', 'Temple', 'Neighbor']
            },
            natasha: {
                'Evening': ['Home', 'Mall', 'Friends'],
                'Night': ['Home', 'Mall', 'Club']
            },
            tanya: {
                'Evening': ['Home', 'Gym', 'Dance Class'],
                'Night': ['Home', 'Club', 'Friends']
            },
            kavya: {
                'Afternoon': ['Home', 'Tuition', 'Library'],
                'Evening': ['Home', 'Park', 'Mall']
            }
        };
        
        const characterVariations = variations[characterId];
        if (!characterVariations) return schedule;
        
        const modifiedSchedule = { ...schedule };
        
        Object.keys(characterVariations).forEach(timeSlot => {
            if (Math.random() < 0.3) { // 30% chance to change this time slot
                const options = characterVariations[timeSlot];
                modifiedSchedule[timeSlot] = options[Math.floor(Math.random() * options.length)];
            }
        });
        
        return modifiedSchedule;
    }
    
    /**
     * Get character location at specific time
     */
    getCharacterLocation(characterId, dayOfWeek, timeSlot) {
        const schedule = this.currentSchedules[characterId]?.[dayOfWeek];
        return schedule?.[timeSlot] || 'Unknown';
    }
    
    /**
     * Get all characters at a specific location and time
     */
    getCharactersAtLocation(location, dayOfWeek, timeSlot) {
        const charactersAtLocation = [];
        
        Object.keys(this.currentSchedules).forEach(characterId => {
            const characterLocation = this.getCharacterLocation(characterId, dayOfWeek, timeSlot);
            if (characterLocation === location) {
                charactersAtLocation.push(characterId);
            }
        });
        
        return charactersAtLocation;
    }
    
    /**
     * Update character locations based on current time
     */
    updateCharacterLocations(dayInfo) {
        const { dayOfWeek, timeSlot } = dayInfo;
        
        Object.keys(this.characterSchedules).forEach(characterId => {
            const newLocation = this.getCharacterLocation(characterId, dayOfWeek, timeSlot);
            const character = this.stateManager.getCharacter(characterId);
            
            if (character && newLocation !== 'Unknown') {
                const oldLocation = character.location;
                character.location = newLocation;
                
                // Emit location change event if location actually changed
                if (oldLocation !== newLocation) {
                    console.log(`${character.name} moved from ${oldLocation} to ${newLocation}`);
                }
            }
        });
    }
    
    /**
     * Find opportunities to interact with character
     */
    findInteractionOpportunities(characterId, dayOfWeek) {
        const schedule = this.currentSchedules[characterId]?.[dayOfWeek];
        if (!schedule) return [];
        
        const opportunities = [];
        
        Object.entries(schedule).forEach(([timeSlot, location]) => {
            // Check if player can access this location
            const worldState = this.stateManager.state.world;
            const locationData = worldState.locations[location];
            
            if (locationData && locationData.unlocked) {
                // Check if character will be alone or with others
                const othersAtLocation = this.getCharactersAtLocation(location, dayOfWeek, timeSlot);
                const privacy = othersAtLocation.length <= 1 ? 'Private' : 'Public';
                
                opportunities.push({
                    timeSlot,
                    location,
                    privacy,
                    risk: this.calculateInteractionRisk(location, timeSlot, privacy),
                    potential: this.calculateInteractionPotential(characterId, location, timeSlot)
                });
            }
        });
        
        return opportunities;
    }
    
    /**
     * Calculate interaction risk
     */
    calculateInteractionRisk(location, timeSlot, privacy) {
        let risk = 0;
        
        // Location-based risk
        const locationRisk = {
            'Home': 2,
            'School': 4,
            'Mall': 3,
            'Temple': 1,
            'Work': 5,
            'Club': 3,
            'Gym': 2
        };
        risk += locationRisk[location] || 3;
        
        // Time-based risk
        const timeRisk = {
            'Morning': 2,
            'School-P1': 4, 'School-P2': 4, 'School-P3': 4, 'School-P4': 4, 'School-P5': 4,
            'Lunch': 3,
            'Afternoon': 2,
            'Evening': 1,
            'Night': 0,
            'Late Night': 0
        };
        risk += timeRisk[timeSlot] || 2;
        
        // Privacy modifier
        if (privacy === 'Private') {
            risk -= 2;
        } else {
            risk += 1;
        }
        
        return Math.max(0, Math.min(10, risk));
    }
    
    /**
     * Calculate interaction potential
     */
    calculateInteractionPotential(characterId, location, timeSlot) {
        const character = this.stateManager.getCharacter(characterId);
        if (!character) return 0;
        
        let potential = 5; // Base potential
        
        // Character state modifiers
        potential += Math.floor(character.stats.lust / 10);
        potential += Math.floor(character.stats.corruption / 20);
        potential -= Math.floor(character.stats.suspicion / 15);
        
        // Location modifiers
        const locationBonus = {
            'Home': 3,
            'Private Room': 5,
            'School': 1,
            'Mall': 2,
            'Temple': -1,
            'Work': -2
        };
        potential += locationBonus[location] || 0;
        
        // Time modifiers
        const timeBonus = {
            'Evening': 2,
            'Night': 3,
            'Late Night': 4
        };
        potential += timeBonus[timeSlot] || 0;
        
        return Math.max(0, Math.min(10, potential));
    }
    
    /**
     * Get character's weekly schedule
     */
    getWeeklySchedule(characterId) {
        return this.currentSchedules[characterId] || {};
    }
    
    /**
     * Check if character is available for interaction
     */
    isCharacterAvailable(characterId, dayOfWeek, timeSlot, playerLocation) {
        const characterLocation = this.getCharacterLocation(characterId, dayOfWeek, timeSlot);
        return characterLocation === playerLocation;
    }
    
    /**
     * Get schedule conflicts for character
     */
    getScheduleConflicts(characterId, dayOfWeek) {
        const schedule = this.currentSchedules[characterId]?.[dayOfWeek];
        if (!schedule) return [];
        
        const conflicts = [];
        
        // Check for suspicious schedule changes
        Object.entries(schedule).forEach(([timeSlot, location]) => {
            if (location === 'Unknown' || location === 'Secret') {
                conflicts.push({
                    timeSlot,
                    type: 'suspicious_activity',
                    description: `${characterId} has suspicious activities during ${timeSlot}`
                });
            }
        });
        
        return conflicts;
    }
    
    /**
     * Save schedule state
     */
    saveState() {
        return {
            currentSchedules: this.currentSchedules
        };
    }
    
    /**
     * Load schedule state
     */
    loadState(state) {
        if (state && state.currentSchedules) {
            this.currentSchedules = state.currentSchedules;
        }
    }
}
