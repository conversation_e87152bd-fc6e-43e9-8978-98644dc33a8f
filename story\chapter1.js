export const chapterData = {
    "start": {
        "text": `
            <p>The pain in your leg is a dull, constant throb, a phantom echo of the crash. It’s been ten days since the funeral. Ten days of living in this small, suffocating Malviya Nagar flat that smells of Phenyl, stale incense sticks, and your mother’s quiet, unending grief.</p>
            <p>You lie on your bed, staring at the peeling paint on the ceiling where a gecko stalks a moth with unnerving patience. The whirring of the old Usha fan struggles against the oppressive late-August humidity, doing little more than stir the thick, sorrowful air. From the living room, you hear the tinny melodrama of a TV serial and your sisters, <span class="character-name" onclick="window.ui.showModal('natasha')">Natasha</span> and <span class="character-name" onclick="window.ui.showModal('tanya')">Tanya</span>, arguing softly about whose turn it is to fill the water bottles for the fridge.</p>
            <p><em>Normal sounds. A normal, shitty life.</em> The thought is flat, devoid of emotion. You've been numb for days.</p>
            <p>Except for the thing that lives in your head now. The ghost in the machine of your consciousness.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Daily Login Bonus Available]</div>
                You've ignored these prompts for days, writing them off as trauma-induced hallucinations. But the memory of the splitting headache that followed your last attempt at defiance—a pain like a hot nail being driven into your skull—forces your hand. You can't afford that again.
            </div>
        `,
        "choices": [
            { "label": "Claim the Bonus. Stop fighting it.", "leadsTo": "choice1_result" }
        ]
    },
    "choice1_result": {
        "onLoad": function() {
            window.player.stats.cp += 10;
            window.player.stats.sp += 1;
        },
        "text": `
            <p>With a sense of weary resignation, you focus your intent. <em>Fine. Yes. Claim.</em></p>
            <div class="system-prompt">
                <div class="prompt-title">[Daily Login Bonus Claimed!]</div>
                <span class="reward">[+10 Corruption Points (CP) added.]</span><br>
                <span class="reward">[+1 System Point (SP) added.]</span>
            </div>
            <p>A strange, faint warmth spreads through your chest, a stark contrast to the cold emptiness that has been your constant companion. It's the first pleasant sensation you’ve felt in weeks. It's immediately followed by a new, more insistent prompt, one that feels less like a notification and more like a command.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Tutorial Quest Chain Initiated: The World Through New Eyes]</div>
                <strong>Quest 1: Know Your Assets.</strong><br>
                <strong>Objective:</strong> The world you knew is a lie. Your first lesson is to learn to see. Open the [Characters] menu and view the profiles of the three female occupants of this dwelling: your mother, <span class="character-name" onclick="window.ui.showModal('sonia')">Sonia</span>, and your sisters, <span class="character-name" onclick="window.ui.showModal('natasha')">Natasha</span> and <span class="character-name" onclick="window.ui.showModal('tanya')">Tanya</span>.<br>
                <span class="reward"><strong>Reward:</strong> +50 CP, +2 SP, Skill Unlocked: [Mental Resonance Scan (Lvl 1)].</span><br>
                <span class="penalty"><strong>Failure Penalty:</strong> Persistent Migraine for 24 hours.</span>
            </div>
            <p>Your blood runs cold. This isn't a random hallucination. It's specific. It's invasive. And it's threatening you. You're not being asked to do something. You're being asked to <em>see</em> differently—to see your own family as... assets.</p>
        `,
        "choices": [
            { "label": "Accept the Quest. See them as the System does.", "leadsTo": "choice2_result" }
        ]
    },
    "choice2_result": {
        "onLoad": function() {
            window.player.stats.cp += 50;
            window.player.stats.sp += 2;
            window.player.skills.push("Mental Resonance Scan (Lvl 1)");
        },
        "text": `
            <p>You accept. The System complies instantly, the profiles blooming in the darkness behind your eyelids. You see your mother, not as the grieving woman you know, but as a collection of crude, terrifying data points. You see your sisters stripped of their identities, reduced to their frustrations and physical measurements. The System has stripped them bare, showing you their secret frustrations, their hidden desires, their deepest vulnerabilities.</p>
            <p>It's disgusting. It's profane. And it gives you a terrifying sense of <strong>power</strong>. The System isn't guessing; it's showing you a truth you never wanted to see, a truth that resonates with a new, cold part of your soul.</p>
            <div class="system-prompt">
                <div class="prompt-title">[QUEST COMPLETE: Know Your Assets]</div>
                <span class="reward">[+50 CP, +2 SP received.]</span><br>
                <span class="reward">[Skill Unlocked: [Mental Resonance Scan (Lvl 1)] - Passively sense the surface emotions and key thought-fragments of designated targets.]</span>
            </div>
            <p>Now that you have the <em>knowledge</em>, the System offers you the <em>means</em> to act on it.</p>
            <div class="system-prompt">
                <div class="prompt-title">[Tutorial Quest Chain Updated]</div>
                <strong>Quest 2: The First Tool.</strong><br>
                <strong>Objective:</strong> The world is a machine of flesh, and you need the right tools to operate it. Open the [System Shop] and purchase your first item.
            </div>
        `,
        "choices": [
            { "label": "Open the System Shop. It's time to arm yourself.", "leadsTo": "choice3_result" }
        ]
    },
    "choice3_result": {
        "text": `
             <p>The Shop menu opens in your mind's eye, a clean interface against the backdrop of your dingy room. You have 60 CP to spend. A small fortune in this new reality.</p>
             <p>Under the [Consumables] tab, you see a list that makes your stomach clench:</p>
             <ul style="list-style-type: square; margin-left: 20px;">
                <li><strong>[Sleeping Pills (Mild)]</strong> - Causes drowsiness, deep sleep for 2-3 hours. Cost: 15 CP.</li>
                <li><strong>[Laxative Powder (Odorless)]</strong> - Creates embarrassing situations, lowers target's confidence. Cost: 20 CP.</li>
                <li><strong>[Aphrodisiac (Low Grade)]</strong> - Increases arousal, lowers inhibitions for a short period. Cost: 50 CP.</li>
             </ul>
             <p>The Aphrodisiac is tempting, a quick path to pleasure. But a deeper, colder instinct takes over. Pleasure is fleeting. <em>Control... control is forever. Control was the real aphrodisiac.</em></p>
        `,
        "choices": [
            { "label": "Purchase [Sleeping Pills (Mild)] x2 for 30 CP.", "leadsTo": "final" }
        ]
    },
    "final": {
        "onLoad": function() {
            window.player.stats.cp -= 30;
            window.player.inventory.push({ item: "Sleeping Pills (Mild)", quantity: 2 });
        },
        "text": `
            <div class="system-prompt">
                <div class="prompt-title">[Purchase Confirmed!]</div>
                <p>[-30 CP. Remaining Balance: 30 CP]</p>
                <p>[Item has been materialized in your bedside drawer.]</p>
                <p>[QUEST COMPLETE: The First Tool]</p>
            </div>
            <p>Your hand trembles slightly as you pull open your bedside drawer. And there they are. A tiny, clear ziplock bag containing two small, unmarked white pills. They are not a hallucination. They are solid. They are real.</p>
            <p>The System is real. The power is real. And the empty void inside you, the one left by your father's death, doesn't feel like a wound anymore. It feels like an empty throne, waiting for its king.</p>
            <hr>
        `,
        "choices": [
            { "label": "The game has begun. Proceed to Chapter 2.", "leadsTo": "LOAD_NEXT_CHAPTER", "chapter": 2 }
        ]
    }
};

export const modals = {
    "sonia": {
        "title": "[Character Profile: Sonia Singh]",
        "content": `
            <p><strong>Designation:</strong> Mother. Primary Asset (Potential).</p>
            <p><strong>Age:</strong> 42</p>
            <p><strong>Status:</strong> Widowed. Financially Stressed (Perceived). <strong>Sexually Starved (Critical).</strong></p>
            <hr>
            <p><strong>Asset Evaluation:</strong> A prime MILF physique, softened by motherhood and neglect. Heavy, sagging breasts (38D) that speak of a history of giving. Wide, birthing hips (40) and a soft belly that men of a certain taste would worship. Years of sexual neglect post-widowhood have created a deep-seated vulnerability and a well of suppressed desire. She is a fortress with unguarded gates, prime for corruption through affection or dominance.</p>
        `
    },
    "natasha": {
        "title": "[Character Profile: Natasha Singh]",
        "content": `
            <p><strong>Designation:</strong> Sister. Secondary Asset.</p>
            <p><strong>Age:</strong> 24</p>
            <p><strong>Status:</strong> Underpaid HR Executive. Frustrated with life. Desperate for male validation and a wealthier lifestyle.</p>
            <hr>
            <p><strong>Asset Evaluation:</strong> A voluptuous, top-heavy build (36D) that she tries to contain within 'respectable' kurtis. Projects a 'sanskari' image as a defense mechanism, but possesses a hidden, almost desperate slutty streak. Highly susceptible to flattery, displays of wealth, and promises of luxury. Her frustration is a key that will unlock her legs.</p>
        `
    },
    "tanya": {
        "title": "[Character Profile: Tanya Singh]",
        "content": `
            <p><strong>Designation:</strong> Sister. Tertiary Asset.</p>
            <p><strong>Age:</strong> 22</p>
            <p><strong>Status:</strong> Underpaid HR Assistant. Fiery temper. Deep-seated inferiority complex masked by aggression.</p>
            <hr>
            <p><strong>Asset Evaluation:</strong> A petite, athletic frame (32B). A classic 'chota packet, bada dhamaka'. Her aggression and fierce independence are a facade covering a deep-seated insecurity about her age and size. She craves control but secretly, desperately wants to be controlled. Responds powerfully to being put in her place by a true alpha. Break her will, and she will become your most loyal attack dog.</p>
        `
    }
};